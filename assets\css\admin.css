/**
 * Enhanced Accessibility Suite - Admin Styles
 * 
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

/* ==========================================================================
   Admin Page Layout
   ========================================================================== */

.eas-admin-wrap {
    max-width: 1200px;
    margin: 0;
}

.eas-admin-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.eas-admin-header h1 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.eas-admin-header .description {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.eas-admin-content {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.eas-settings-sections {
    flex: 2;
}

.eas-admin-sidebar {
    flex: 1;
    max-width: 300px;
}

/* ==========================================================================
   Settings Sections
   ========================================================================== */

.eas-settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
}

.eas-settings-section h2 {
    background: #f1f1f1;
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 15px 20px;
    font-size: 16px;
    color: #23282d;
}

.eas-settings-section .form-table {
    margin: 0;
}

.eas-settings-section .form-table th {
    padding: 20px;
    width: 200px;
    vertical-align: top;
}

.eas-settings-section .form-table td {
    padding: 20px;
}

/* ==========================================================================
   Form Elements
   ========================================================================== */

.eas-settings-section input[type="range"] {
    width: 200px;
    margin-right: 10px;
}

.eas-settings-section input[type="number"] {
    width: 80px;
    margin-right: 5px;
}

.eas-settings-section select {
    min-width: 150px;
}

.eas-settings-section label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.eas-settings-section .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Range slider styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #0073aa;
    border-radius: 50%;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #0073aa;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* ==========================================================================
   Admin Actions
   ========================================================================== */

.eas-admin-actions {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
}

.eas-admin-actions .button {
    margin-right: 10px;
}

#eas-reset-settings {
    color: #a00;
    border-color: #a00;
}

#eas-reset-settings:hover {
    background: #a00;
    color: #fff;
}

/* ==========================================================================
   Sidebar
   ========================================================================== */

.eas-info-box,
.eas-support-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.eas-info-box h3,
.eas-support-box h3 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

.eas-info-box h4 {
    margin: 15px 0 10px 0;
    color: #23282d;
    font-size: 14px;
}

.eas-info-box p,
.eas-support-box p {
    margin: 0 0 15px 0;
    color: #666;
    line-height: 1.5;
}

.eas-info-box ul {
    margin: 0;
    padding-left: 20px;
}

.eas-info-box li {
    margin-bottom: 5px;
    color: #666;
}

/* ==========================================================================
   Notifications
   ========================================================================== */

.eas-notice {
    background: #fff;
    border-left: 4px solid #0073aa;
    padding: 12px;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

.eas-notice.success {
    border-left-color: #46b450;
}

.eas-notice.error {
    border-left-color: #dc3232;
}

.eas-notice.warning {
    border-left-color: #ffb900;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.eas-loading {
    opacity: 0.6;
    pointer-events: none;
}

.eas-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: eas-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes eas-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .eas-admin-content {
        flex-direction: column;
    }
    
    .eas-admin-sidebar {
        max-width: none;
    }
    
    .eas-settings-section .form-table th {
        width: auto;
        padding: 15px;
    }
    
    .eas-settings-section .form-table td {
        padding: 15px;
    }
    
    .eas-settings-section input[type="range"] {
        width: 150px;
    }
}

@media (max-width: 480px) {
    .eas-admin-wrap {
        margin: 0 -20px;
    }
    
    .eas-admin-header,
    .eas-settings-section,
    .eas-admin-actions,
    .eas-info-box,
    .eas-support-box {
        margin-left: 20px;
        margin-right: 20px;
        border-radius: 0;
    }
    
    .eas-settings-section .form-table th,
    .eas-settings-section .form-table td {
        display: block;
        width: 100%;
        padding: 10px 15px;
    }
    
    .eas-settings-section .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .eas-settings-section .form-table td {
        padding-top: 5px;
    }
}
