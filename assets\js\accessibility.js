/**
 * Enhanced Accessibility Suite - Frontend JavaScript
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

(function($) {
    'use strict';

    /**
     * Main Accessibility Suite Class
     */
    class AccessibilitySuite {
        constructor() {
            this.options = easOptions.options || {};
            this.strings = easOptions.strings || {};
            this.isMenuOpen = false;
            this.currentFontSize = this.options.default_font_size || 16;
            this.isDarkMode = false;
            this.isHighContrast = false;
            this.isReading = false;
            this.currentUtterance = null;

            // Load saved preferences
            this.loadPreferences();

            // Initialize features
            this.init();
        }

        /**
         * Initialize all accessibility features
         */
        init() {
            this.createAccessibilityMenu();
            this.initTextToSpeech();
            this.initKeyboardNavigation();
            this.initFocusIndicators();
            this.applyStoredSettings();
            this.bindEvents();
        }

        /**
         * Load user preferences from localStorage
         */
        loadPreferences() {
            const stored = localStorage.getItem('eas_preferences');
            if (stored) {
                try {
                    const preferences = JSON.parse(stored);
                    this.currentFontSize = preferences.fontSize || this.options.default_font_size;
                    this.isDarkMode = preferences.darkMode || false;
                    this.isHighContrast = preferences.highContrast || false;
                } catch (e) {
                    console.warn('EAS: Could not parse stored preferences');
                }
            }
        }

        /**
         * Save user preferences to localStorage
         */
        savePreferences() {
            const preferences = {
                fontSize: this.currentFontSize,
                darkMode: this.isDarkMode,
                highContrast: this.isHighContrast
            };
            localStorage.setItem('eas_preferences', JSON.stringify(preferences));
        }

        /**
         * Create the main accessibility menu
         */
        createAccessibilityMenu() {
            // Create menu container
            const menuContainer = document.createElement('div');
            menuContainer.className = 'eas-accessibility-menu';
            menuContainer.setAttribute('role', 'dialog');
            menuContainer.setAttribute('aria-label', this.strings.accessibilityMenu);
            menuContainer.setAttribute('aria-hidden', 'true');

            // Create toggle button
            const toggleButton = document.createElement('button');
            toggleButton.className = 'eas-menu-toggle';
            toggleButton.innerHTML = '<span class="eas-icon">♿</span>';
            toggleButton.setAttribute('aria-label', this.strings.accessibilityMenu);
            toggleButton.setAttribute('aria-expanded', 'false');
            toggleButton.setAttribute('aria-controls', 'eas-menu-panel');

            // Create menu panel
            const menuPanel = document.createElement('div');
            menuPanel.className = 'eas-menu-panel';
            menuPanel.id = 'eas-menu-panel';

            // Add menu items
            this.createMenuItems(menuPanel);

            // Assemble menu
            menuContainer.appendChild(toggleButton);
            menuContainer.appendChild(menuPanel);

            // Position menu based on settings
            this.positionMenu(menuContainer);

            // Add to page
            document.body.appendChild(menuContainer);

            // Store references
            this.menuContainer = menuContainer;
            this.toggleButton = toggleButton;
            this.menuPanel = menuPanel;

            // Bind toggle event
            toggleButton.addEventListener('click', () => this.toggleMenu());

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!menuContainer.contains(e.target) && this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isMenuOpen) {
                    this.closeMenu();
                    toggleButton.focus();
                }
            });
        }

        /**
         * Create menu items based on enabled features
         */
        createMenuItems(panel) {
            const menuItems = [];

            // Background toggle
            if (this.options.enable_background_toggle) {
                menuItems.push({
                    id: 'background-toggle',
                    text: this.strings.toggleBackground,
                    action: () => this.toggleBackground()
                });
            }

            // Font size controls
            if (this.options.enable_font_size_controls) {
                menuItems.push({
                    id: 'font-increase',
                    text: this.strings.increaseFontSize,
                    action: () => this.increaseFontSize()
                });

                menuItems.push({
                    id: 'font-decrease',
                    text: this.strings.decreaseFontSize,
                    action: () => this.decreaseFontSize()
                });

                menuItems.push({
                    id: 'font-reset',
                    text: this.strings.resetFontSize,
                    action: () => this.resetFontSize()
                });
            }

            // Contrast controls
            if (this.options.enable_contrast_controls) {
                menuItems.push({
                    id: 'contrast-toggle',
                    text: this.strings.highContrast,
                    action: () => this.toggleContrast()
                });
            }

            // Text-to-speech controls
            if (this.options.enable_text_to_speech) {
                menuItems.push({
                    id: 'speech-toggle',
                    text: this.strings.readAloud,
                    action: () => this.toggleSpeech()
                });
            }

            // Create menu item elements
            menuItems.forEach(item => {
                const button = document.createElement('button');
                button.className = 'eas-menu-item';
                button.id = `eas-${item.id}`;
                button.textContent = item.text;
                button.setAttribute('role', 'menuitem');
                button.addEventListener('click', item.action);
                panel.appendChild(button);
            });
        }
        /**
         * Position menu based on settings
         */
        positionMenu(container) {
            const position = this.options.button_position || 'top-right';
            container.classList.add(`eas-position-${position}`);
        }

        /**
         * Toggle menu visibility
         */
        toggleMenu() {
            if (this.isMenuOpen) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        }

        /**
         * Open accessibility menu
         */
        openMenu() {
            this.isMenuOpen = true;
            this.menuPanel.style.display = 'block';
            this.toggleButton.setAttribute('aria-expanded', 'true');
            this.menuContainer.setAttribute('aria-hidden', 'false');

            // Focus first menu item
            const firstItem = this.menuPanel.querySelector('.eas-menu-item');
            if (firstItem) {
                firstItem.focus();
            }

            // Add keyboard navigation
            this.addMenuKeyboardNavigation();
        }

        /**
         * Close accessibility menu
         */
        closeMenu() {
            this.isMenuOpen = false;
            this.menuPanel.style.display = 'none';
            this.toggleButton.setAttribute('aria-expanded', 'false');
            this.menuContainer.setAttribute('aria-hidden', 'true');
        }

        /**
         * Add keyboard navigation to menu
         */
        addMenuKeyboardNavigation() {
            const menuItems = this.menuPanel.querySelectorAll('.eas-menu-item');

            menuItems.forEach((item, index) => {
                item.addEventListener('keydown', (e) => {
                    switch (e.key) {
                        case 'ArrowDown':
                            e.preventDefault();
                            const nextIndex = (index + 1) % menuItems.length;
                            menuItems[nextIndex].focus();
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            const prevIndex = (index - 1 + menuItems.length) % menuItems.length;
                            menuItems[prevIndex].focus();
                            break;
                        case 'Home':
                            e.preventDefault();
                            menuItems[0].focus();
                            break;
                        case 'End':
                            e.preventDefault();
                            menuItems[menuItems.length - 1].focus();
                            break;
                    }
                });
            });
        }

        /**
         * Toggle background color (dark/light mode)
         */
        toggleBackground() {
            this.isDarkMode = !this.isDarkMode;

            if (this.isDarkMode) {
                document.body.classList.add('eas-dark-mode');
                document.body.style.backgroundColor = '#1a1a1a';
                document.body.style.color = '#ffffff';
            } else {
                document.body.classList.remove('eas-dark-mode');
                document.body.style.backgroundColor = '';
                document.body.style.color = '';
            }

            this.savePreferences();
            this.updateMenuItemText('background-toggle',
                this.isDarkMode ? 'Light Mode' : this.strings.toggleBackground);
        }

        /**
         * Increase font size
         */
        increaseFontSize() {
            const maxSize = this.options.max_font_size || 24;
            if (this.currentFontSize < maxSize) {
                this.currentFontSize += 2;
                this.applyFontSize();
                this.savePreferences();
            }
        }

        /**
         * Decrease font size
         */
        decreaseFontSize() {
            const minSize = this.options.min_font_size || 12;
            if (this.currentFontSize > minSize) {
                this.currentFontSize -= 2;
                this.applyFontSize();
                this.savePreferences();
            }
        }

        /**
         * Reset font size to default
         */
        resetFontSize() {
            this.currentFontSize = this.options.default_font_size || 16;
            this.applyFontSize();
            this.savePreferences();
        }

        /**
         * Apply current font size to page
         */
        applyFontSize() {
            document.documentElement.style.fontSize = this.currentFontSize + 'px';
        }

        /**
         * Toggle high contrast mode
         */
        toggleContrast() {
            this.isHighContrast = !this.isHighContrast;

            if (this.isHighContrast) {
                document.body.classList.add('eas-high-contrast');
            } else {
                document.body.classList.remove('eas-high-contrast');
            }

            this.savePreferences();
            this.updateMenuItemText('contrast-toggle',
                this.isHighContrast ? this.strings.normalContrast : this.strings.highContrast);
        }

        /**
         * Initialize text-to-speech functionality
         */
        initTextToSpeech() {
            if (!this.options.enable_text_to_speech || !('speechSynthesis' in window)) {
                return;
            }

            // Add click listeners to readable elements
            const readableElements = 'p, h1, h2, h3, h4, h5, h6, li, blockquote, article, section';
            document.querySelectorAll(readableElements).forEach(element => {
                element.classList.add('eas-readable');
                element.setAttribute('tabindex', '0');
                element.setAttribute('role', 'button');
                element.setAttribute('aria-label', 'Click to read aloud');

                element.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.readElement(element);
                });

                element.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.readElement(element);
                    }
                });
            });
        }
        /**
         * Read element text aloud
         */
        readElement(element) {
            const text = element.textContent.trim();
            if (!text) return;

            // Stop current speech
            speechSynthesis.cancel();

            // Create utterance
            this.currentUtterance = new SpeechSynthesisUtterance(text);
            this.currentUtterance.rate = this.options.speech_rate || 1.0;
            this.currentUtterance.pitch = this.options.speech_pitch || 1.0;
            this.currentUtterance.volume = this.options.speech_volume || 1.0;

            // Add visual feedback
            element.classList.add('eas-reading');

            // Handle speech events
            this.currentUtterance.onend = () => {
                element.classList.remove('eas-reading');
                this.isReading = false;
            };

            this.currentUtterance.onerror = () => {
                element.classList.remove('eas-reading');
                this.isReading = false;
            };

            // Start speech
            speechSynthesis.speak(this.currentUtterance);
            this.isReading = true;
        }

        /**
         * Toggle speech (start/stop)
         */
        toggleSpeech() {
            if (this.isReading) {
                speechSynthesis.cancel();
                this.isReading = false;
                this.updateMenuItemText('speech-toggle', this.strings.readAloud);
            } else {
                // Read the main content
                const mainContent = document.querySelector('main, .content, #content, article') || document.body;
                this.readElement(mainContent);
                this.updateMenuItemText('speech-toggle', this.strings.stopReading);
            }
        }

        /**
         * Initialize keyboard navigation enhancements
         */
        initKeyboardNavigation() {
            if (!this.options.enable_keyboard_navigation) {
                return;
            }

            // Add skip links
            this.addSkipLinks();

            // Enhance tab navigation
            this.enhanceTabNavigation();

            // Add keyboard shortcuts
            this.addKeyboardShortcuts();
        }

        /**
         * Add skip links for keyboard navigation
         */
        addSkipLinks() {
            const skipLinks = document.createElement('div');
            skipLinks.className = 'eas-skip-links';
            skipLinks.setAttribute('aria-label', 'Skip links');

            const links = [
                { href: '#main', text: 'Skip to main content' },
                { href: '#navigation', text: 'Skip to navigation' },
                { href: '#footer', text: 'Skip to footer' }
            ];

            links.forEach(link => {
                const skipLink = document.createElement('a');
                skipLink.href = link.href;
                skipLink.textContent = link.text;
                skipLink.className = 'eas-skip-link';
                skipLinks.appendChild(skipLink);
            });

            document.body.insertBefore(skipLinks, document.body.firstChild);
        }

        /**
         * Enhance tab navigation
         */
        enhanceTabNavigation() {
            // Ensure all interactive elements are focusable
            const interactiveElements = 'a, button, input, select, textarea, [tabindex]';
            document.querySelectorAll(interactiveElements).forEach(element => {
                if (!element.hasAttribute('tabindex') && element.tabIndex < 0) {
                    element.tabIndex = 0;
                }
            });
        }

        /**
         * Add keyboard shortcuts
         */
        addKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Alt + A: Open accessibility menu
                if (e.altKey && e.key === 'a') {
                    e.preventDefault();
                    this.toggleMenu();
                }

                // Alt + D: Toggle dark mode
                if (e.altKey && e.key === 'd' && this.options.enable_background_toggle) {
                    e.preventDefault();
                    this.toggleBackground();
                }

                // Alt + +: Increase font size
                if (e.altKey && e.key === '+' && this.options.enable_font_size_controls) {
                    e.preventDefault();
                    this.increaseFontSize();
                }

                // Alt + -: Decrease font size
                if (e.altKey && e.key === '-' && this.options.enable_font_size_controls) {
                    e.preventDefault();
                    this.decreaseFontSize();
                }

                // Alt + C: Toggle contrast
                if (e.altKey && e.key === 'c' && this.options.enable_contrast_controls) {
                    e.preventDefault();
                    this.toggleContrast();
                }
            });
        }

        /**
         * Initialize focus indicators
         */
        initFocusIndicators() {
            if (!this.options.enable_focus_indicators) {
                return;
            }

            document.body.classList.add('eas-enhanced-focus');
        }

        /**
         * Apply stored settings on page load
         */
        applyStoredSettings() {
            if (this.isDarkMode) {
                this.toggleBackground();
            }

            if (this.isHighContrast) {
                this.toggleContrast();
            }

            if (this.currentFontSize !== (this.options.default_font_size || 16)) {
                this.applyFontSize();
            }
        }

        /**
         * Bind additional events
         */
        bindEvents() {
            // Handle window resize
            window.addEventListener('resize', () => {
                if (this.isMenuOpen) {
                    this.closeMenu();
                }
            });

            // Handle page visibility change
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && this.isReading) {
                    speechSynthesis.pause();
                } else if (!document.hidden && this.isReading) {
                    speechSynthesis.resume();
                }
            });
        }

        /**
         * Update menu item text
         */
        updateMenuItemText(itemId, newText) {
            const menuItem = document.getElementById(`eas-${itemId}`);
            if (menuItem) {
                menuItem.textContent = newText;
            }
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        if (typeof easOptions !== 'undefined') {
            new AccessibilitySuite();
        }
    });

})(jQuery);