/**
 * Enhanced Accessibility Suite - Admin JavaScript
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

(function($) {
    'use strict';

    /**
     * Admin functionality class
     */
    class EASAdmin {
        constructor() {
            this.strings = easAdmin.strings || {};
            this.ajaxUrl = easAdmin.ajaxUrl;
            this.nonce = easAdmin.nonce;
            this.isLoading = false;

            this.init();
        }

        /**
         * Initialize admin functionality
         */
        init() {
            this.bindEvents();
            this.initRangeSliders();
            this.initConditionalFields();
            this.initFormValidation();
        }

        /**
         * Bind event handlers
         */
        bindEvents() {
            // Settings form submission
            $('#eas-settings-form').on('submit', (e) => this.handleFormSubmit(e));
            
            // Reset settings button
            $('#eas-reset-settings').on('click', (e) => this.handleResetSettings(e));
            
            // Feature toggle changes
            $('input[name="eas_options[enable_text_to_speech]"]').on('change', () => {
                this.toggleConditionalSection('#tts-settings', $('input[name="eas_options[enable_text_to_speech]"]').is(':checked'));
            });
            
            $('input[name="eas_options[enable_font_size_controls]"]').on('change', () => {
                this.toggleConditionalSection('#font-settings', $('input[name="eas_options[enable_font_size_controls]"]').is(':checked'));
            });

            $('input[name="eas_options[enable_custom_colors]"]').on('change', () => {
                this.toggleConditionalSection('#color-settings', $('input[name="eas_options[enable_custom_colors]"]').is(':checked'));
            });

            // Preview functionality
            this.initPreviewMode();

            // Import/Export functionality
            this.initImportExport();
        }

        /**
         * Initialize range sliders with live updates
         */
        initRangeSliders() {
            // Speech rate slider
            $('#speech_rate').on('input', function() {
                $('#speech_rate_value').text($(this).val());
            });

            // Speech pitch slider
            $('#speech_pitch').on('input', function() {
                $('#speech_pitch_value').text($(this).val());
            });

            // Speech volume slider
            $('#speech_volume').on('input', function() {
                $('#speech_volume_value').text($(this).val());
            });
        }

        /**
         * Initialize conditional field visibility
         */
        initConditionalFields() {
            // Show/hide TTS settings based on checkbox
            const ttsEnabled = $('input[name="eas_options[enable_text_to_speech]"]').is(':checked');
            this.toggleConditionalSection('#tts-settings', ttsEnabled);

            // Show/hide font settings based on checkbox
            const fontEnabled = $('input[name="eas_options[enable_font_size_controls]"]').is(':checked');
            this.toggleConditionalSection('#font-settings', fontEnabled);

            // Show/hide color settings based on checkbox
            const colorEnabled = $('input[name="eas_options[enable_custom_colors]"]').is(':checked');
            this.toggleConditionalSection('#color-settings', colorEnabled);
        }

        /**
         * Toggle conditional section visibility
         */
        toggleConditionalSection(sectionId, show) {
            if (show) {
                $(sectionId).slideDown(300);
            } else {
                $(sectionId).slideUp(300);
            }
        }

        /**
         * Initialize form validation
         */
        initFormValidation() {
            // Validate font size ranges
            $('#default_font_size, #max_font_size, #min_font_size').on('change', () => {
                this.validateFontSizes();
            });
        }

        /**
         * Validate font size settings
         */
        validateFontSizes() {
            const defaultSize = parseInt($('#default_font_size').val());
            const maxSize = parseInt($('#max_font_size').val());
            const minSize = parseInt($('#min_font_size').val());

            let isValid = true;
            let message = '';

            if (minSize >= maxSize) {
                message = 'Minimum font size must be less than maximum font size.';
                isValid = false;
            } else if (defaultSize < minSize || defaultSize > maxSize) {
                message = 'Default font size must be between minimum and maximum values.';
                isValid = false;
            }

            if (!isValid) {
                this.showNotice(message, 'error');
                return false;
            }

            return true;
        }

        /**
         * Handle form submission
         */
        handleFormSubmit(e) {
            e.preventDefault();

            if (this.isLoading) {
                return;
            }

            // Validate form
            if (!this.validateFontSizes()) {
                return;
            }

            this.isLoading = true;
            this.showLoading(true);

            const formData = new FormData();
            formData.append('action', 'eas_save_settings');
            formData.append('nonce', this.nonce);

            // Collect form data
            const options = {};
            $('#eas-settings-form').find('input, select').each(function() {
                const $input = $(this);
                const name = $input.attr('name');
                
                if (name && name.startsWith('eas_options[')) {
                    const key = name.replace('eas_options[', '').replace(']', '');
                    
                    if ($input.attr('type') === 'checkbox') {
                        options[key] = $input.is(':checked');
                    } else {
                        options[key] = $input.val();
                    }
                }
            });

            formData.append('options', JSON.stringify(options));

            // Send AJAX request
            $.ajax({
                url: this.ajaxUrl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => this.handleSaveSuccess(response),
                error: (xhr, status, error) => this.handleSaveError(xhr, status, error),
                complete: () => {
                    this.isLoading = false;
                    this.showLoading(false);
                }
            });
        }

        /**
         * Handle successful save
         */
        handleSaveSuccess(response) {
            if (response.success) {
                this.showNotice(response.data.message || this.strings.settingsSaved, 'success');
            } else {
                this.showNotice(response.data || this.strings.error, 'error');
            }
        }

        /**
         * Handle save error
         */
        handleSaveError(xhr, status, error) {
            console.error('Save error:', error);
            this.showNotice(this.strings.error, 'error');
        }

        /**
         * Handle reset settings
         */
        handleResetSettings(e) {
            e.preventDefault();

            if (!confirm(this.strings.confirmReset)) {
                return;
            }

            if (this.isLoading) {
                return;
            }

            this.isLoading = true;
            this.showLoading(true);

            $.ajax({
                url: this.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'eas_reset_settings',
                    nonce: this.nonce
                },
                success: (response) => this.handleResetSuccess(response),
                error: (xhr, status, error) => this.handleResetError(xhr, status, error),
                complete: () => {
                    this.isLoading = false;
                    this.showLoading(false);
                }
            });
        }

        /**
         * Handle successful reset
         */
        handleResetSuccess(response) {
            if (response.success) {
                this.showNotice(response.data.message || this.strings.settingsReset, 'success');
                
                // Reload page to show reset values
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                this.showNotice(response.data || this.strings.error, 'error');
            }
        }

        /**
         * Handle reset error
         */
        handleResetError(xhr, status, error) {
            console.error('Reset error:', error);
            this.showNotice(this.strings.error, 'error');
        }

        /**
         * Show loading state
         */
        showLoading(show) {
            if (show) {
                $('.eas-admin-wrap').addClass('eas-loading');
                $('#submit, #eas-reset-settings').prop('disabled', true);
            } else {
                $('.eas-admin-wrap').removeClass('eas-loading');
                $('#submit, #eas-reset-settings').prop('disabled', false);
            }
        }

        /**
         * Show admin notice
         */
        showNotice(message, type = 'info') {
            // Remove existing notices
            $('.eas-notice').remove();

            const notice = $(`
                <div class="eas-notice ${type}">
                    <p>${message}</p>
                </div>
            `);

            $('.eas-admin-header').after(notice);

            // Auto-hide success notices
            if (type === 'success') {
                setTimeout(() => {
                    notice.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }
        }

        /**
         * Initialize preview mode
         */
        initPreviewMode() {
            // Add preview button
            const previewButton = $(`
                <button type="button" id="eas-preview-mode" class="button button-secondary">
                    <span class="dashicons dashicons-visibility"></span> Preview Changes
                </button>
            `);

            $('.eas-admin-actions').prepend(previewButton);

            // Handle preview toggle
            previewButton.on('click', () => this.togglePreviewMode());
        }

        /**
         * Toggle preview mode
         */
        togglePreviewMode() {
            const $button = $('#eas-preview-mode');
            const isPreview = $button.hasClass('active');

            if (isPreview) {
                // Exit preview mode
                $button.removeClass('active').html('<span class="dashicons dashicons-visibility"></span> Preview Changes');
                this.exitPreviewMode();
            } else {
                // Enter preview mode
                $button.addClass('active').html('<span class="dashicons dashicons-hidden"></span> Exit Preview');
                this.enterPreviewMode();
            }
        }

        /**
         * Enter preview mode
         */
        enterPreviewMode() {
            // Apply current settings to admin page for preview
            const fontSize = $('#default_font_size').val();
            const darkMode = $('input[name="eas_options[enable_background_toggle]"]').is(':checked');
            const highContrast = $('input[name="eas_options[enable_contrast_controls]"]').is(':checked');

            if (fontSize) {
                $('body').css('font-size', fontSize + 'px');
            }

            this.showNotice('Preview mode active - you can see how your settings will look.', 'info');
        }

        /**
         * Exit preview mode
         */
        exitPreviewMode() {
            // Reset admin page styles
            $('body').css('font-size', '');
            $('.eas-notice').remove();
        }

        /**
         * Initialize import/export functionality
         */
        initImportExport() {
            // Add import/export buttons
            const importExportSection = $(`
                <div class="eas-import-export-section">
                    <h3>Import/Export Settings</h3>
                    <div class="eas-import-export-controls">
                        <button type="button" id="eas-export-settings" class="button button-secondary">
                            <span class="dashicons dashicons-download"></span> Export Settings
                        </button>
                        <div class="eas-import-controls">
                            <input type="file" id="eas-import-file" accept=".json" style="display: none;">
                            <button type="button" id="eas-import-settings" class="button button-secondary">
                                <span class="dashicons dashicons-upload"></span> Import Settings
                            </button>
                        </div>
                    </div>
                    <p class="description">Export your current settings or import settings from another site.</p>
                </div>
            `);

            $('.eas-admin-actions').before(importExportSection);

            // Bind events
            $('#eas-export-settings').on('click', () => this.exportSettings());
            $('#eas-import-settings').on('click', () => $('#eas-import-file').click());
            $('#eas-import-file').on('change', (e) => this.importSettings(e));
        }

        /**
         * Export settings to JSON file
         */
        exportSettings() {
            // Collect current form data
            const settings = {};
            $('#eas-settings-form').find('input, select').each(function() {
                const $input = $(this);
                const name = $input.attr('name');

                if (name && name.startsWith('eas_options[')) {
                    const key = name.replace('eas_options[', '').replace(']', '');

                    if ($input.attr('type') === 'checkbox') {
                        settings[key] = $input.is(':checked');
                    } else {
                        settings[key] = $input.val();
                    }
                }
            });

            // Create export object
            const exportData = {
                plugin: 'Enhanced Accessibility Suite',
                version: '2.0.0',
                exported: new Date().toISOString(),
                settings: settings
            };

            // Download as JSON file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `eas-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showNotice('Settings exported successfully!', 'success');
        }

        /**
         * Import settings from JSON file
         */
        importSettings(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const importData = JSON.parse(e.target.result);

                    // Validate import data
                    if (!importData.settings || importData.plugin !== 'Enhanced Accessibility Suite') {
                        this.showNotice('Invalid settings file. Please select a valid EAS export file.', 'error');
                        return;
                    }

                    // Apply imported settings to form
                    Object.keys(importData.settings).forEach(key => {
                        const $input = $(`[name="eas_options[${key}]"]`);
                        if ($input.length) {
                            if ($input.attr('type') === 'checkbox') {
                                $input.prop('checked', importData.settings[key]);
                            } else {
                                $input.val(importData.settings[key]);
                            }
                        }
                    });

                    // Update conditional sections
                    this.initConditionalFields();

                    // Update range slider displays
                    this.initRangeSliders();

                    this.showNotice('Settings imported successfully! Remember to save your changes.', 'success');

                } catch (error) {
                    this.showNotice('Error reading settings file. Please check the file format.', 'error');
                }
            };

            reader.readAsText(file);

            // Reset file input
            event.target.value = '';
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        if (typeof easAdmin !== 'undefined') {
            new EASAdmin();
        }
    });

})(jQuery);
