/**
 * Enhanced Accessibility Suite - Premium Features Styles
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

/* ==========================================================================
   Custom Color Schemes
   ========================================================================== */

.eas-custom-colors .eas-menu-toggle {
    background-color: var(--eas-primary-color, #0073aa);
    border: 2px solid var(--eas-secondary-color, #005a87);
}

.eas-custom-colors .eas-menu-toggle:hover {
    background-color: var(--eas-secondary-color, #005a87);
    box-shadow: 0 0 0 3px var(--eas-accent-color, #ffb900);
}

.eas-custom-colors .eas-menu-toggle:focus {
    outline: 3px solid var(--eas-accent-color, #ffb900);
}

.eas-custom-colors .eas-menu-panel {
    border-color: var(--eas-primary-color, #0073aa);
}

.eas-custom-colors .eas-menu-item:focus {
    background-color: var(--eas-primary-color, #0073aa);
    color: white;
}

.eas-custom-colors .eas-menu-item:hover {
    background-color: rgba(var(--eas-primary-rgb, 0, 115, 170), 0.1);
}

/* ==========================================================================
   Reading Guide
   ========================================================================== */

.eas-reading-guide {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--eas-guide-height, 3px);
    background-color: var(--eas-guide-color, #ff6b6b);
    z-index: 999998;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

.eas-reading-guide.active {
    opacity: 1;
}

.eas-reading-guide::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to bottom, 
        rgba(255, 107, 107, 0.3) 0%, 
        transparent 100%);
}

/* ==========================================================================
   Dyslexia-Friendly Font
   ========================================================================== */

.eas-dyslexia-font {
    font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
}

.eas-dyslexia-font * {
    font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
    letter-spacing: 0.12em !important;
    word-spacing: 0.16em !important;
    line-height: 1.5 !important;
}

.eas-dyslexia-font h1, 
.eas-dyslexia-font h2, 
.eas-dyslexia-font h3, 
.eas-dyslexia-font h4, 
.eas-dyslexia-font h5, 
.eas-dyslexia-font h6 {
    font-weight: bold !important;
    margin-bottom: 0.5em !important;
}

.eas-dyslexia-font p {
    margin-bottom: 1em !important;
}

/* ==========================================================================
   Animation Controls
   ========================================================================== */

/* Slow animations */
.eas-animations-slow .eas-menu-toggle,
.eas-animations-slow .eas-menu-item,
.eas-animations-slow .eas-readable {
    transition-duration: 0.6s !important;
}

.eas-animations-slow .eas-reading {
    animation-duration: 2.5s !important;
}

/* Normal animations (default) */
.eas-animations-normal .eas-menu-toggle,
.eas-animations-normal .eas-menu-item,
.eas-animations-normal .eas-readable {
    transition-duration: 0.3s !important;
}

.eas-animations-normal .eas-reading {
    animation-duration: 1.5s !important;
}

/* Fast animations */
.eas-animations-fast .eas-menu-toggle,
.eas-animations-fast .eas-menu-item,
.eas-animations-fast .eas-readable {
    transition-duration: 0.15s !important;
}

.eas-animations-fast .eas-reading {
    animation-duration: 0.8s !important;
}

/* No animations */
.eas-animations-none * {
    transition: none !important;
    animation: none !important;
}

/* ==========================================================================
   Enhanced Focus Indicators (Premium)
   ========================================================================== */

.eas-premium-focus *:focus {
    outline: 3px solid var(--eas-accent-color, #ffb900) !important;
    outline-offset: 3px !important;
    box-shadow: 
        0 0 0 6px rgba(var(--eas-accent-rgb, 255, 185, 0), 0.3),
        0 0 15px rgba(var(--eas-accent-rgb, 255, 185, 0), 0.2) !important;
    position: relative !important;
    z-index: 1000 !important;
}

.eas-premium-focus *:focus::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 2px dashed var(--eas-accent-color, #ffb900);
    border-radius: 4px;
    pointer-events: none;
    z-index: -1;
}

/* ==========================================================================
   Text Highlighting Enhancements
   ========================================================================== */

.eas-premium-highlight {
    background: linear-gradient(120deg, 
        transparent 0%, 
        var(--eas-accent-color, #ffb900) 0%, 
        var(--eas-accent-color, #ffb900) 100%, 
        transparent 100%) !important;
    background-size: 100% 0.3em;
    background-repeat: no-repeat;
    background-position: 0 88%;
    transition: background-size 0.25s ease-in !important;
}

.eas-premium-highlight:hover {
    background-size: 100% 100%;
    color: #000 !important;
}

/* ==========================================================================
   Advanced Menu Styles
   ========================================================================== */

.eas-premium-menu .eas-menu-panel {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(var(--eas-primary-rgb, 0, 115, 170), 0.2);
    box-shadow: 
        0 10px 40px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
}

.eas-premium-menu .eas-menu-item {
    position: relative;
    overflow: hidden;
}

.eas-premium-menu .eas-menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(var(--eas-primary-rgb, 0, 115, 170), 0.1), 
        transparent);
    transition: left 0.5s ease;
}

.eas-premium-menu .eas-menu-item:hover::before {
    left: 100%;
}

/* ==========================================================================
   Accessibility Report Styles
   ========================================================================== */

.eas-accessibility-report {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid var(--eas-primary-color, #0073aa);
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    max-height: 70vh;
    overflow-y: auto;
    z-index: 1000000;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.eas-report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.eas-report-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.eas-report-score {
    text-align: center;
    margin: 20px 0;
}

.eas-score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin: 0 auto;
}

.eas-score-excellent { background-color: #4caf50; }
.eas-score-good { background-color: #8bc34a; }
.eas-score-fair { background-color: #ff9800; }
.eas-score-poor { background-color: #f44336; }

.eas-report-section {
    margin: 15px 0;
}

.eas-report-section h4 {
    margin: 0 0 10px 0;
    color: var(--eas-primary-color, #0073aa);
}

.eas-report-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.eas-report-status {
    font-weight: bold;
}

.eas-status-pass { color: #4caf50; }
.eas-status-warning { color: #ff9800; }
.eas-status-fail { color: #f44336; }

/* ==========================================================================
   Responsive Premium Features
   ========================================================================== */

@media (max-width: 768px) {
    .eas-accessibility-report {
        max-width: 90vw;
        max-height: 80vh;
        padding: 15px;
    }
    
    .eas-reading-guide {
        height: calc(var(--eas-guide-height, 3px) * 1.5);
    }
    
    .eas-premium-menu .eas-menu-panel {
        backdrop-filter: none;
        background: rgba(255, 255, 255, 0.98);
    }
}

@media (prefers-reduced-motion: reduce) {
    .eas-premium-menu .eas-menu-item::before,
    .eas-premium-highlight,
    .eas-reading-guide {
        transition: none !important;
        animation: none !important;
    }
}
