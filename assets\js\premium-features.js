/**
 * Enhanced Accessibility Suite - Premium Features JavaScript
 *
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

(function($) {
    'use strict';

    /**
     * Premium Features Class
     */
    class EASPremiumFeatures {
        constructor(mainInstance) {
            this.main = mainInstance;
            this.options = mainInstance.options;
            this.strings = mainInstance.strings;
            
            // Premium feature states
            this.readingGuideActive = false;
            this.dyslexiaFontActive = false;
            this.currentSpeechPreset = 'normal';
            this.analyticsData = {
                featuresUsed: {},
                sessionStart: Date.now(),
                interactions: 0
            };

            this.init();
        }

        /**
         * Initialize premium features
         */
        init() {
            this.initCustomColors();
            this.initReadingGuide();
            this.initDyslexiaFont();
            this.initSpeechPresets();
            this.initAnimationControls();
            this.initAccessibilityReport();
            this.initAnalytics();
            this.addPremiumMenuItems();
        }

        /**
         * Initialize custom color scheme
         */
        initCustomColors() {
            if (!this.options.enable_custom_colors) return;

            // Apply custom CSS variables
            const root = document.documentElement;
            root.style.setProperty('--eas-primary-color', this.options.custom_primary_color);
            root.style.setProperty('--eas-secondary-color', this.options.custom_secondary_color);
            root.style.setProperty('--eas-accent-color', this.options.custom_accent_color);
            
            // Convert hex to RGB for rgba usage
            const primaryRgb = this.hexToRgb(this.options.custom_primary_color);
            const accentRgb = this.hexToRgb(this.options.custom_accent_color);
            
            if (primaryRgb) {
                root.style.setProperty('--eas-primary-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
            }
            if (accentRgb) {
                root.style.setProperty('--eas-accent-rgb', `${accentRgb.r}, ${accentRgb.g}, ${accentRgb.b}`);
            }

            document.body.classList.add('eas-custom-colors');
        }

        /**
         * Initialize reading guide
         */
        initReadingGuide() {
            if (!this.options.enable_reading_guide) return;

            // Create reading guide element
            this.readingGuide = document.createElement('div');
            this.readingGuide.className = 'eas-reading-guide';
            this.readingGuide.style.setProperty('--eas-guide-color', this.options.reading_guide_color);
            this.readingGuide.style.setProperty('--eas-guide-height', this.options.reading_guide_height + 'px');
            document.body.appendChild(this.readingGuide);

            // Track mouse movement
            document.addEventListener('mousemove', (e) => {
                if (this.readingGuideActive) {
                    this.updateReadingGuide(e.clientY);
                }
            });

            // Track scroll
            window.addEventListener('scroll', () => {
                if (this.readingGuideActive) {
                    this.updateReadingGuideOnScroll();
                }
            });
        }

        /**
         * Update reading guide position
         */
        updateReadingGuide(mouseY) {
            if (this.readingGuide) {
                this.readingGuide.style.top = (mouseY + window.scrollY - this.options.reading_guide_height / 2) + 'px';
            }
        }

        /**
         * Update reading guide on scroll
         */
        updateReadingGuideOnScroll() {
            // Keep guide visible during scroll
            if (this.readingGuide && this.readingGuideActive) {
                this.readingGuide.classList.add('active');
            }
        }

        /**
         * Toggle reading guide
         */
        toggleReadingGuide() {
            this.readingGuideActive = !this.readingGuideActive;
            
            if (this.readingGuideActive) {
                this.readingGuide.classList.add('active');
                this.trackFeatureUsage('reading_guide');
            } else {
                this.readingGuide.classList.remove('active');
            }

            this.main.updateMenuItemText('reading-guide', 
                this.readingGuideActive ? 'Disable Reading Guide' : 'Enable Reading Guide');
        }

        /**
         * Initialize dyslexia-friendly font
         */
        initDyslexiaFont() {
            if (!this.options.enable_dyslexia_font) return;

            // Load OpenDyslexic font if available
            this.loadDyslexicFont();
        }

        /**
         * Load dyslexic font
         */
        loadDyslexicFont() {
            const link = document.createElement('link');
            link.href = 'https://fonts.googleapis.com/css2?family=OpenDyslexic:wght@400;700&display=swap';
            link.rel = 'stylesheet';
            document.head.appendChild(link);
        }

        /**
         * Toggle dyslexia font
         */
        toggleDyslexiaFont() {
            this.dyslexiaFontActive = !this.dyslexiaFontActive;
            
            if (this.dyslexiaFontActive) {
                document.body.classList.add('eas-dyslexia-font');
                this.trackFeatureUsage('dyslexia_font');
            } else {
                document.body.classList.remove('eas-dyslexia-font');
            }

            this.main.updateMenuItemText('dyslexia-font', 
                this.dyslexiaFontActive ? 'Disable Dyslexia Font' : 'Enable Dyslexia Font');
        }

        /**
         * Initialize speech presets
         */
        initSpeechPresets() {
            if (!this.options.enable_text_to_speech || !this.options.speech_presets) return;

            this.speechPresets = this.options.speech_presets;
        }

        /**
         * Apply speech preset
         */
        applySpeechPreset(presetName) {
            if (!this.speechPresets[presetName]) return;

            const preset = this.speechPresets[presetName];
            this.main.options.speech_rate = preset.rate;
            this.main.options.speech_pitch = preset.pitch;
            this.main.options.speech_volume = preset.volume;
            this.currentSpeechPreset = presetName;

            this.trackFeatureUsage('speech_preset_' + presetName);
        }

        /**
         * Initialize animation controls
         */
        initAnimationControls() {
            if (!this.options.enable_animations) return;

            const speed = this.options.animation_speed || 'normal';
            document.body.classList.add(`eas-animations-${speed}`);
        }

        /**
         * Initialize accessibility report
         */
        initAccessibilityReport() {
            if (!this.options.enable_accessibility_report) return;

            this.reportData = {
                score: 0,
                issues: [],
                recommendations: []
            };
        }

        /**
         * Generate accessibility report
         */
        generateAccessibilityReport() {
            const report = this.analyzePageAccessibility();
            this.displayAccessibilityReport(report);
            this.trackFeatureUsage('accessibility_report');
        }

        /**
         * Analyze page accessibility
         */
        analyzePageAccessibility() {
            const issues = [];
            const recommendations = [];
            let score = 100;

            // Check for alt text on images
            const images = document.querySelectorAll('img');
            let imagesWithoutAlt = 0;
            images.forEach(img => {
                if (!img.alt || img.alt.trim() === '') {
                    imagesWithoutAlt++;
                }
            });

            if (imagesWithoutAlt > 0) {
                issues.push(`${imagesWithoutAlt} images missing alt text`);
                score -= Math.min(20, imagesWithoutAlt * 2);
            }

            // Check for heading structure
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            if (headings.length === 0) {
                issues.push('No headings found');
                score -= 15;
            }

            // Check for form labels
            const inputs = document.querySelectorAll('input, select, textarea');
            let inputsWithoutLabels = 0;
            inputs.forEach(input => {
                const label = document.querySelector(`label[for="${input.id}"]`);
                if (!label && !input.getAttribute('aria-label')) {
                    inputsWithoutLabels++;
                }
            });

            if (inputsWithoutLabels > 0) {
                issues.push(`${inputsWithoutLabels} form inputs missing labels`);
                score -= Math.min(15, inputsWithoutLabels * 3);
            }

            // Check color contrast (simplified)
            const textElements = document.querySelectorAll('p, span, div, a, button');
            let lowContrastElements = 0;
            // This is a simplified check - in a real implementation, you'd use a proper contrast calculation
            textElements.forEach(element => {
                const style = window.getComputedStyle(element);
                const color = style.color;
                const backgroundColor = style.backgroundColor;
                // Simplified contrast check
                if (color === backgroundColor) {
                    lowContrastElements++;
                }
            });

            // Generate recommendations
            if (issues.length === 0) {
                recommendations.push('Great job! No major accessibility issues found.');
            } else {
                recommendations.push('Consider adding alt text to all images');
                recommendations.push('Ensure proper heading hierarchy');
                recommendations.push('Add labels to all form inputs');
                recommendations.push('Check color contrast ratios');
            }

            return {
                score: Math.max(0, score),
                issues: issues,
                recommendations: recommendations
            };
        }

        /**
         * Display accessibility report
         */
        displayAccessibilityReport(report) {
            // Remove existing report
            const existingReport = document.querySelector('.eas-accessibility-report');
            if (existingReport) {
                existingReport.remove();
            }

            // Create report modal
            const reportModal = document.createElement('div');
            reportModal.className = 'eas-accessibility-report';
            reportModal.innerHTML = `
                <div class="eas-report-header">
                    <h3>Accessibility Report</h3>
                    <button class="eas-report-close" aria-label="Close report">&times;</button>
                </div>
                <div class="eas-report-score">
                    <div class="eas-score-circle ${this.getScoreClass(report.score)}">
                        ${report.score}%
                    </div>
                    <p>${this.getScoreDescription(report.score)}</p>
                </div>
                <div class="eas-report-section">
                    <h4>Issues Found (${report.issues.length})</h4>
                    ${report.issues.length > 0 ? 
                        report.issues.map(issue => `<div class="eas-report-item">
                            <span>${issue}</span>
                            <span class="eas-report-status eas-status-fail">Fix</span>
                        </div>`).join('') : 
                        '<p>No issues found!</p>'
                    }
                </div>
                <div class="eas-report-section">
                    <h4>Recommendations</h4>
                    ${report.recommendations.map(rec => `<div class="eas-report-item">
                        <span>${rec}</span>
                    </div>`).join('')}
                </div>
            `;

            document.body.appendChild(reportModal);

            // Close button functionality
            reportModal.querySelector('.eas-report-close').addEventListener('click', () => {
                reportModal.remove();
            });

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    reportModal.remove();
                }
            });
        }

        /**
         * Get score class for styling
         */
        getScoreClass(score) {
            if (score >= 90) return 'eas-score-excellent';
            if (score >= 70) return 'eas-score-good';
            if (score >= 50) return 'eas-score-fair';
            return 'eas-score-poor';
        }

        /**
         * Get score description
         */
        getScoreDescription(score) {
            if (score >= 90) return 'Excellent accessibility!';
            if (score >= 70) return 'Good accessibility';
            if (score >= 50) return 'Fair accessibility';
            return 'Needs improvement';
        }

        /**
         * Initialize analytics
         */
        initAnalytics() {
            if (!this.options.enable_usage_analytics) return;

            // Track page load
            this.trackEvent('page_load');
            
            // Track feature usage
            window.addEventListener('beforeunload', () => {
                this.saveAnalyticsData();
            });
        }

        /**
         * Track feature usage
         */
        trackFeatureUsage(feature) {
            if (!this.options.enable_usage_analytics) return;

            this.analyticsData.interactions++;
            if (!this.analyticsData.featuresUsed[feature]) {
                this.analyticsData.featuresUsed[feature] = 0;
            }
            this.analyticsData.featuresUsed[feature]++;
        }

        /**
         * Track event
         */
        trackEvent(eventName, data = {}) {
            if (!this.options.enable_usage_analytics) return;

            // Store in localStorage for now (in production, you'd send to analytics service)
            const events = JSON.parse(localStorage.getItem('eas_analytics_events') || '[]');
            events.push({
                event: eventName,
                data: data,
                timestamp: Date.now()
            });
            
            // Keep only last 100 events
            if (events.length > 100) {
                events.splice(0, events.length - 100);
            }
            
            localStorage.setItem('eas_analytics_events', JSON.stringify(events));
        }

        /**
         * Save analytics data
         */
        saveAnalyticsData() {
            if (!this.options.enable_usage_analytics) return;

            this.analyticsData.sessionDuration = Date.now() - this.analyticsData.sessionStart;
            localStorage.setItem('eas_analytics_session', JSON.stringify(this.analyticsData));
        }

        /**
         * Add premium menu items
         */
        addPremiumMenuItems() {
            const menuPanel = this.main.menuPanel;
            if (!menuPanel) return;

            // Reading guide toggle
            if (this.options.enable_reading_guide) {
                const readingGuideBtn = document.createElement('button');
                readingGuideBtn.className = 'eas-menu-item';
                readingGuideBtn.id = 'eas-reading-guide';
                readingGuideBtn.textContent = 'Enable Reading Guide';
                readingGuideBtn.addEventListener('click', () => this.toggleReadingGuide());
                menuPanel.appendChild(readingGuideBtn);
            }

            // Dyslexia font toggle
            if (this.options.enable_dyslexia_font) {
                const dyslexiaBtn = document.createElement('button');
                dyslexiaBtn.className = 'eas-menu-item';
                dyslexiaBtn.id = 'eas-dyslexia-font';
                dyslexiaBtn.textContent = 'Enable Dyslexia Font';
                dyslexiaBtn.addEventListener('click', () => this.toggleDyslexiaFont());
                menuPanel.appendChild(dyslexiaBtn);
            }

            // Accessibility report
            if (this.options.enable_accessibility_report) {
                const reportBtn = document.createElement('button');
                reportBtn.className = 'eas-menu-item';
                reportBtn.id = 'eas-accessibility-report';
                reportBtn.textContent = 'Accessibility Report';
                reportBtn.addEventListener('click', () => this.generateAccessibilityReport());
                menuPanel.appendChild(reportBtn);
            }
        }

        /**
         * Convert hex color to RGB
         */
        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
    }

    // Initialize premium features when main accessibility suite is ready
    $(document).ready(function() {
        if (typeof easOptions !== 'undefined' && window.AccessibilitySuite) {
            // Wait for main instance to be created
            setTimeout(() => {
                const mainInstance = window.easInstance;
                if (mainInstance) {
                    window.easPremiumFeatures = new EASPremiumFeatures(mainInstance);
                }
            }, 100);
        }
    });

})(jQuery);
