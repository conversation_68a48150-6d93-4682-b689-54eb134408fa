=== Enhanced Accessibility Suite ===
Contributors: yourname
Donate link: https://your-website.com/donate
Tags: accessibility, a11y, wcag, text-to-speech, high-contrast, font-size, keyboard-navigation, screen-reader
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 2.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A comprehensive accessibility enhancement plugin featuring text-to-speech, visual adjustments, keyboard navigation, and customizable accessibility tools.

== Description ==

Enhanced Accessibility Suite is a powerful WordPress plugin designed to make your website more accessible and inclusive for all visitors, including those with disabilities. The plugin provides a comprehensive set of tools that help improve website usability and ensure compliance with accessibility standards.

= Key Features =

* **Text-to-Speech Functionality** - Allow visitors to have content read aloud with customizable voice settings
* **Visual Adjustments** - Dark/light mode toggle, high contrast mode, and font size controls
* **Keyboard Navigation** - Enhanced keyboard accessibility with skip links and navigation helpers
* **Focus Indicators** - Improved visibility of focused elements for better navigation
* **Customizable Interface** - Flexible positioning and styling options for the accessibility menu
* **Mobile Responsive** - Fully optimized for mobile devices and tablets
* **WCAG Compliant** - Helps meet Web Content Accessibility Guidelines standards

= Perfect For =

* Government websites requiring accessibility compliance
* Educational institutions and libraries
* Healthcare and medical websites
* E-commerce sites wanting to reach all customers
* Corporate websites with accessibility policies
* Any website committed to inclusive design

= Premium Features =

* Advanced color scheme customization
* Accessibility compliance reporting
* Usage analytics and insights
* Multiple language support
* Custom CSS injection
* Priority support

= Easy to Use =

The plugin works out of the box with sensible defaults, but offers extensive customization options for advanced users. The floating accessibility menu can be positioned anywhere on your site and provides instant access to all accessibility features.

= Developer Friendly =

* Clean, well-documented code
* Extensive hooks and filters for customization
* Translation ready with .pot file included
* Follows WordPress coding standards
* Regular updates and maintenance

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/enhanced-accessibility-suite` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Use the Settings->Accessibility Suite screen to configure the plugin
4. The accessibility menu will automatically appear on your frontend

= Manual Installation =

1. Download the plugin zip file
2. Extract the files to your `/wp-content/plugins/` directory
3. Activate the plugin in your WordPress admin panel
4. Configure settings under Settings > Accessibility Suite

== Frequently Asked Questions ==

= Does this plugin slow down my website? =

No, the plugin is optimized for performance and only loads necessary assets. The JavaScript and CSS files are minified and only loaded when needed.

= Is the plugin compatible with my theme? =

Yes, the plugin is designed to work with any properly coded WordPress theme. It uses non-intrusive methods to add accessibility features.

= Can I customize the appearance of the accessibility menu? =

Yes, the plugin provides several positioning options and styling can be customized through CSS. Premium version offers more customization options.

= Does this make my site fully accessible? =

While this plugin significantly improves accessibility, full compliance depends on your content and theme. The plugin helps with many common accessibility issues but should be part of a broader accessibility strategy.

= Can I disable specific features? =

Yes, each feature can be individually enabled or disabled through the admin settings panel.

= Is the plugin translation ready? =

Yes, the plugin includes a .pot file and is ready for translation into any language.

== Screenshots ==

1. Accessibility menu in action on the frontend
2. Admin settings panel with all configuration options
3. Text-to-speech functionality highlighting text being read
4. High contrast mode demonstration
5. Font size adjustment controls
6. Mobile responsive accessibility menu

== Changelog ==

= 2.0.0 =
* Major rewrite with improved performance
* Added text-to-speech functionality
* Enhanced keyboard navigation
* Improved mobile responsiveness
* Better admin interface
* Added AJAX settings saving
* Improved accessibility compliance
* Added preview functionality
* Better error handling
* Code optimization and cleanup

= 1.5.0 =
* Added high contrast mode
* Improved focus indicators
* Better mobile support
* Bug fixes and improvements

= 1.0.0 =
* Initial release
* Basic accessibility features
* Font size controls
* Dark mode toggle

== Upgrade Notice ==

= 2.0.0 =
Major update with new features and improved performance. Backup your site before upgrading.

== Support ==

For support, please visit our [support forum](https://your-website.com/support) or [contact us directly](https://your-website.com/contact).

== Privacy Policy ==

This plugin does not collect any personal data from your website visitors. All accessibility preferences are stored locally in the user's browser using localStorage.

== Credits ==

* Icons provided by [Dashicons](https://developer.wordpress.org/resource/dashicons/)
* Accessibility guidelines based on [WCAG 2.1](https://www.w3.org/WAI/WCAG21/quickref/)
* Tested with screen readers including NVDA, JAWS, and VoiceOver

== Roadmap ==

* Voice command integration
* AI-powered content simplification
* Advanced analytics dashboard
* Multi-site network support
* Integration with popular page builders
