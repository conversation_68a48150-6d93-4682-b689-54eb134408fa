<?php
/**
 * Admin page template for Enhanced Accessibility Suite
 * 
 * @since 2.0.0
 * @package Enhanced_Accessibility_Suite
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$options = eas()->get_option();
?>

<div class="wrap eas-admin-wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="eas-admin-header">
        <p class="description">
            <?php _e('Configure accessibility features to enhance your website\'s usability for all visitors.', EAS_TEXT_DOMAIN); ?>
        </p>
    </div>
    
    <div class="eas-admin-content">
        <form id="eas-settings-form" method="post" action="options.php">
            <?php settings_fields('eas_options_group'); ?>
            
            <div class="eas-settings-sections">
                
                <!-- General Settings -->
                <div class="eas-settings-section">
                    <h2><?php _e('General Settings', EAS_TEXT_DOMAIN); ?></h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Button Position', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <select name="eas_options[button_position]" id="button_position">
                                    <option value="top-left" <?php selected($options['button_position'], 'top-left'); ?>>
                                        <?php _e('Top Left', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="top-right" <?php selected($options['button_position'], 'top-right'); ?>>
                                        <?php _e('Top Right', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="bottom-left" <?php selected($options['button_position'], 'bottom-left'); ?>>
                                        <?php _e('Bottom Left', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="bottom-right" <?php selected($options['button_position'], 'bottom-right'); ?>>
                                        <?php _e('Bottom Right', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Choose where to display the accessibility menu button.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- Feature Toggles -->
                <div class="eas-settings-section">
                    <h2><?php _e('Feature Settings', EAS_TEXT_DOMAIN); ?></h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Text-to-Speech', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_text_to_speech]" value="1" 
                                           <?php checked($options['enable_text_to_speech']); ?> />
                                    <?php _e('Enable text-to-speech functionality', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Allow users to have text read aloud by clicking on paragraphs.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Background Toggle', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_background_toggle]" value="1" 
                                           <?php checked($options['enable_background_toggle']); ?> />
                                    <?php _e('Enable background color toggle', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Allow users to switch between light and dark backgrounds.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Font Size Controls', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_font_size_controls]" value="1" 
                                           <?php checked($options['enable_font_size_controls']); ?> />
                                    <?php _e('Enable font size adjustment controls', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Allow users to increase or decrease text size.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Contrast Controls', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_contrast_controls]" value="1" 
                                           <?php checked($options['enable_contrast_controls']); ?> />
                                    <?php _e('Enable high contrast mode', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Provide high contrast color scheme for better visibility.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Focus Indicators', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_focus_indicators]" value="1" 
                                           <?php checked($options['enable_focus_indicators']); ?> />
                                    <?php _e('Enhanced focus indicators', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Improve visibility of focused elements for keyboard navigation.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Keyboard Navigation', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_keyboard_navigation]" value="1" 
                                           <?php checked($options['enable_keyboard_navigation']); ?> />
                                    <?php _e('Enhanced keyboard navigation', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Improve keyboard accessibility with skip links and navigation helpers.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Premium Features -->
                <div class="eas-settings-section">
                    <h2><?php _e('Premium Features', EAS_TEXT_DOMAIN); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Custom Colors', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_custom_colors]" value="1"
                                           <?php checked($options['enable_custom_colors']); ?> />
                                    <?php _e('Enable custom color schemes', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Allow customization of menu colors and themes.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Reading Guide', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_reading_guide]" value="1"
                                           <?php checked($options['enable_reading_guide']); ?> />
                                    <?php _e('Enable reading guide line', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Provides a colored line that follows the mouse to help with reading.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Dyslexia Font', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_dyslexia_font]" value="1"
                                           <?php checked($options['enable_dyslexia_font']); ?> />
                                    <?php _e('Enable dyslexia-friendly font', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Provides OpenDyslexic font option for users with dyslexia.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Accessibility Report', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_accessibility_report]" value="1"
                                           <?php checked($options['enable_accessibility_report']); ?> />
                                    <?php _e('Enable accessibility compliance reporting', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Provides detailed accessibility analysis and recommendations.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Usage Analytics', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="eas_options[enable_usage_analytics]" value="1"
                                           <?php checked($options['enable_usage_analytics']); ?> />
                                    <?php _e('Enable usage analytics (privacy-friendly)', EAS_TEXT_DOMAIN); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Track feature usage to improve accessibility (data stored locally).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Text-to-Speech Settings -->
                <div class="eas-settings-section" id="tts-settings" style="<?php echo $options['enable_text_to_speech'] ? '' : 'display:none;'; ?>">
                    <h2><?php _e('Text-to-Speech Settings', EAS_TEXT_DOMAIN); ?></h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Speech Rate', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="range" name="eas_options[speech_rate]" id="speech_rate" 
                                       min="0.1" max="3.0" step="0.1" value="<?php echo esc_attr($options['speech_rate']); ?>" />
                                <span id="speech_rate_value"><?php echo esc_html($options['speech_rate']); ?></span>
                                <p class="description">
                                    <?php _e('Control the speed of speech synthesis (0.1 = very slow, 3.0 = very fast).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Speech Pitch', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="range" name="eas_options[speech_pitch]" id="speech_pitch" 
                                       min="0.1" max="2.0" step="0.1" value="<?php echo esc_attr($options['speech_pitch']); ?>" />
                                <span id="speech_pitch_value"><?php echo esc_html($options['speech_pitch']); ?></span>
                                <p class="description">
                                    <?php _e('Control the pitch of speech synthesis (0.1 = very low, 2.0 = very high).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Speech Volume', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="range" name="eas_options[speech_volume]" id="speech_volume" 
                                       min="0.1" max="1.0" step="0.1" value="<?php echo esc_attr($options['speech_volume']); ?>" />
                                <span id="speech_volume_value"><?php echo esc_html($options['speech_volume']); ?></span>
                                <p class="description">
                                    <?php _e('Control the volume of speech synthesis (0.1 = very quiet, 1.0 = full volume).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <!-- Font Size Settings -->
                <div class="eas-settings-section" id="font-settings" style="<?php echo $options['enable_font_size_controls'] ? '' : 'display:none;'; ?>">
                    <h2><?php _e('Font Size Settings', EAS_TEXT_DOMAIN); ?></h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Default Font Size', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="number" name="eas_options[default_font_size]" id="default_font_size" 
                                       min="10" max="30" value="<?php echo esc_attr($options['default_font_size']); ?>" />
                                <span>px</span>
                                <p class="description">
                                    <?php _e('Default font size in pixels (10-30).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Maximum Font Size', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="number" name="eas_options[max_font_size]" id="max_font_size" 
                                       min="16" max="40" value="<?php echo esc_attr($options['max_font_size']); ?>" />
                                <span>px</span>
                                <p class="description">
                                    <?php _e('Maximum allowed font size in pixels (16-40).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Minimum Font Size', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="number" name="eas_options[min_font_size]" id="min_font_size" 
                                       min="8" max="20" value="<?php echo esc_attr($options['min_font_size']); ?>" />
                                <span>px</span>
                                <p class="description">
                                    <?php _e('Minimum allowed font size in pixels (8-20).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Color Customization Settings -->
                <div class="eas-settings-section" id="color-settings" style="<?php echo $options['enable_custom_colors'] ? '' : 'display:none;'; ?>">
                    <h2><?php _e('Color Customization', EAS_TEXT_DOMAIN); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Primary Color', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="color" name="eas_options[custom_primary_color]" id="custom_primary_color"
                                       value="<?php echo esc_attr($options['custom_primary_color']); ?>" />
                                <p class="description">
                                    <?php _e('Main color for buttons and interactive elements.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Secondary Color', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="color" name="eas_options[custom_secondary_color]" id="custom_secondary_color"
                                       value="<?php echo esc_attr($options['custom_secondary_color']); ?>" />
                                <p class="description">
                                    <?php _e('Secondary color for hover states and borders.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Accent Color', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="color" name="eas_options[custom_accent_color]" id="custom_accent_color"
                                       value="<?php echo esc_attr($options['custom_accent_color']); ?>" />
                                <p class="description">
                                    <?php _e('Accent color for focus indicators and highlights.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Reading Guide Color', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="color" name="eas_options[reading_guide_color]" id="reading_guide_color"
                                       value="<?php echo esc_attr($options['reading_guide_color']); ?>" />
                                <p class="description">
                                    <?php _e('Color for the reading guide line.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Reading Guide Height', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <input type="number" name="eas_options[reading_guide_height]" id="reading_guide_height"
                                       min="1" max="10" value="<?php echo esc_attr($options['reading_guide_height']); ?>" />
                                <span>px</span>
                                <p class="description">
                                    <?php _e('Height of the reading guide line in pixels (1-10).', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Animation Speed', EAS_TEXT_DOMAIN); ?></th>
                            <td>
                                <select name="eas_options[animation_speed]" id="animation_speed">
                                    <option value="slow" <?php selected($options['animation_speed'], 'slow'); ?>>
                                        <?php _e('Slow', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="normal" <?php selected($options['animation_speed'], 'normal'); ?>>
                                        <?php _e('Normal', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="fast" <?php selected($options['animation_speed'], 'fast'); ?>>
                                        <?php _e('Fast', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                    <option value="none" <?php selected($options['animation_speed'], 'none'); ?>>
                                        <?php _e('No Animations', EAS_TEXT_DOMAIN); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Control the speed of animations and transitions.', EAS_TEXT_DOMAIN); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="eas-admin-actions">
                <?php submit_button(__('Save Settings', EAS_TEXT_DOMAIN), 'primary', 'submit', false); ?>
                <button type="button" id="eas-reset-settings" class="button button-secondary">
                    <?php _e('Reset to Defaults', EAS_TEXT_DOMAIN); ?>
                </button>
            </div>
        </form>
    </div>
    
    <div class="eas-admin-sidebar">
        <div class="eas-info-box">
            <h3><?php _e('About Enhanced Accessibility Suite', EAS_TEXT_DOMAIN); ?></h3>
            <p><?php _e('This plugin provides comprehensive accessibility features to make your website more inclusive and user-friendly for visitors with disabilities.', EAS_TEXT_DOMAIN); ?></p>
            
            <h4><?php _e('Features Include:', EAS_TEXT_DOMAIN); ?></h4>
            <ul>
                <li><?php _e('Text-to-speech functionality', EAS_TEXT_DOMAIN); ?></li>
                <li><?php _e('Background color toggle', EAS_TEXT_DOMAIN); ?></li>
                <li><?php _e('Font size controls', EAS_TEXT_DOMAIN); ?></li>
                <li><?php _e('High contrast mode', EAS_TEXT_DOMAIN); ?></li>
                <li><?php _e('Enhanced focus indicators', EAS_TEXT_DOMAIN); ?></li>
                <li><?php _e('Keyboard navigation improvements', EAS_TEXT_DOMAIN); ?></li>
            </ul>
        </div>
        
        <div class="eas-support-box">
            <h3><?php _e('Need Help?', EAS_TEXT_DOMAIN); ?></h3>
            <p><?php _e('Visit our documentation or contact support for assistance.', EAS_TEXT_DOMAIN); ?></p>
            <a href="#" class="button button-secondary" target="_blank">
                <?php _e('Documentation', EAS_TEXT_DOMAIN); ?>
            </a>
        </div>
    </div>
</div>
