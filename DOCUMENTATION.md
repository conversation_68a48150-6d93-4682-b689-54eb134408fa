# Enhanced Accessibility Suite - User Documentation

## Table of Contents
1. [Installation](#installation)
2. [Getting Started](#getting-started)
3. [Features Overview](#features-overview)
4. [Configuration](#configuration)
5. [Customization](#customization)
6. [Troubleshooting](#troubleshooting)
7. [Developer Guide](#developer-guide)

## Installation

### Automatic Installation
1. Log in to your WordPress admin panel
2. Navigate to **Plugins > Add New**
3. Search for "Enhanced Accessibility Suite"
4. Click **Install Now** and then **Activate**

### Manual Installation
1. Download the plugin zip file
2. Upload to `/wp-content/plugins/` directory
3. Extract the files
4. Activate the plugin in **Plugins > Installed Plugins**

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- Modern web browser with JavaScript enabled

## Getting Started

Once activated, the plugin works immediately with default settings. You'll see:

1. **Accessibility Menu Button**: A floating button (♿) appears on your website
2. **Admin Settings**: Access via **Settings > Accessibility Suite**
3. **Automatic Features**: Enhanced focus indicators and keyboard navigation

### Quick Setup
1. Go to **Settings > Accessibility Suite**
2. Enable desired features (all are enabled by default)
3. Adjust positioning and settings as needed
4. Save changes

## Features Overview

### Text-to-Speech
- Click any paragraph, heading, or text element to hear it read aloud
- Customizable speech rate, pitch, and volume
- Visual highlighting of text being read
- Works with screen readers and assistive technologies

**Usage:**
- Click the "Read Aloud" button in the accessibility menu
- Or click directly on any text element
- Use keyboard shortcuts: Alt+R to toggle reading

### Visual Adjustments

#### Dark Mode Toggle
- Switch between light and dark color schemes
- Preserves user preference across sessions
- Reduces eye strain in low-light conditions

#### High Contrast Mode
- Increases color contrast for better visibility
- Helpful for users with visual impairments
- WCAG AA compliant contrast ratios

#### Font Size Controls
- Increase/decrease text size dynamically
- Maintains layout integrity
- Customizable size limits (8px - 40px)

### Keyboard Navigation
- Enhanced tab navigation with visible focus indicators
- Skip links for main content, navigation, and footer
- Keyboard shortcuts for common actions
- Improved screen reader compatibility

**Keyboard Shortcuts:**
- `Alt + A`: Open accessibility menu
- `Alt + D`: Toggle dark mode
- `Alt + +`: Increase font size
- `Alt + -`: Decrease font size
- `Alt + C`: Toggle high contrast
- `Escape`: Close accessibility menu

### Mobile Optimization
- Touch-friendly interface on mobile devices
- Responsive design adapts to screen size
- Gesture support for accessibility features

## Configuration

### General Settings

#### Button Position
Choose where the accessibility menu button appears:
- Top Left
- Top Right (default)
- Bottom Left
- Bottom Right

#### Feature Toggles
Enable or disable individual features:
- Text-to-Speech
- Background Toggle (Dark Mode)
- Font Size Controls
- Contrast Controls
- Focus Indicators
- Keyboard Navigation

### Text-to-Speech Settings

#### Speech Rate
- Range: 0.1 (very slow) to 3.0 (very fast)
- Default: 1.0 (normal speed)
- Adjust based on user preference and content complexity

#### Speech Pitch
- Range: 0.1 (very low) to 2.0 (very high)
- Default: 1.0 (normal pitch)
- Higher pitch may be easier to understand for some users

#### Speech Volume
- Range: 0.1 (very quiet) to 1.0 (full volume)
- Default: 1.0 (full volume)
- Respects system volume settings

### Font Size Settings

#### Default Font Size
- Range: 10px to 30px
- Default: 16px
- Base font size when page loads

#### Maximum Font Size
- Range: 16px to 40px
- Default: 24px
- Upper limit for font size increases

#### Minimum Font Size
- Range: 8px to 20px
- Default: 12px
- Lower limit for font size decreases

## Customization

### CSS Customization
Add custom styles to your theme's CSS or use the WordPress Customizer:

```css
/* Customize accessibility menu button */
.eas-menu-toggle {
    background-color: #your-color !important;
    border-radius: 8px !important;
}

/* Customize menu panel */
.eas-menu-panel {
    background-color: #your-background !important;
    border: 2px solid #your-border-color !important;
}

/* Customize dark mode colors */
.eas-dark-mode {
    background-color: #your-dark-bg !important;
    color: #your-dark-text !important;
}
```

### PHP Hooks and Filters

#### Modify Default Options
```php
add_filter('eas_default_options', function($defaults) {
    $defaults['button_position'] = 'bottom-left';
    $defaults['speech_rate'] = 0.8;
    return $defaults;
});
```

#### Add Custom Menu Items
```php
add_action('eas_menu_items', function() {
    echo '<button class="eas-menu-item" onclick="myCustomFunction()">Custom Feature</button>';
});
```

#### Modify Readable Elements
```php
add_filter('eas_readable_selectors', function($selectors) {
    $selectors .= ', .my-custom-content';
    return $selectors;
});
```

## Troubleshooting

### Common Issues

#### Accessibility Menu Not Appearing
1. Check if JavaScript is enabled in browser
2. Verify no JavaScript errors in browser console
3. Check for plugin conflicts by deactivating other plugins
4. Ensure theme is not overriding plugin styles

#### Text-to-Speech Not Working
1. Verify browser supports Speech Synthesis API
2. Check browser permissions for speech
3. Ensure volume is not muted
4. Try different browser or device

#### Font Size Changes Not Applied
1. Check if theme has !important CSS rules overriding changes
2. Verify font size limits in plugin settings
3. Clear browser cache and reload page

#### Keyboard Navigation Issues
1. Ensure keyboard navigation is enabled in settings
2. Check for conflicting keyboard shortcuts
3. Verify browser allows keyboard navigation

### Browser Compatibility
- **Chrome**: Full support for all features
- **Firefox**: Full support for all features
- **Safari**: Full support (iOS 14.5+ for speech synthesis)
- **Edge**: Full support for all features
- **Internet Explorer**: Limited support (not recommended)

### Performance Optimization
- Plugin assets are minified and optimized
- Features load only when needed
- Local storage used for user preferences
- No external dependencies or API calls

## Developer Guide

### File Structure
```
enhanced-accessibility-suite/
├── assets/
│   ├── css/
│   │   ├── accessibility.css
│   │   └── admin.css
│   └── js/
│       ├── accessibility.js
│       └── admin.js
├── includes/
│   └── admin-page.php
├── languages/
│   └── enhanced-accessibility-suite.pot
├── my-accessibility-plugin.php
├── readme.txt
├── CHANGELOG.md
└── DOCUMENTATION.md
```

### Class Structure
- `Enhanced_Accessibility_Suite`: Main plugin class
- `AccessibilitySuite`: Frontend JavaScript class
- `EASAdmin`: Admin panel JavaScript class

### Database Options
- `eas_options`: Stores all plugin settings
- Uses WordPress options API for data persistence
- Automatic cleanup on plugin uninstall

### Security Features
- Nonce verification for all AJAX requests
- Capability checks for admin functions
- Input sanitization and validation
- XSS and CSRF protection

### Translation
The plugin is translation-ready. To translate:
1. Use the included `.pot` file
2. Create `.po` and `.mo` files for your language
3. Place in `/languages/` directory
4. Use WordPress translation functions

For more advanced customization and development information, please refer to the inline code documentation and WordPress Plugin Development guidelines.
